"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, LogOut, User, Settings, CreditCard, Hash, Phone, Copy, Check, RefreshCw } from "lucide-react"

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("hash")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [hashValue, setHashValue] = useState("")
  const [result, setResult] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [error, setError] = useState("")

  const handleHashPhone = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!phoneNumber) {
      setError("Please enter a phone number")
      return
    }

    // Basic validation for Kenyan phone numbers
    const phoneRegex = /^(?:\+254|0)[17]\d{8}$/
    if (!phoneRegex.test(phoneNumber)) {
      setError("Please enter a valid Kenyan phone number")
      return
    }

    setError("")
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Generate a fake hash for demo purposes
      const fakeHash = Array.from({ length: 64 }, () => "0123456789abcdef"[Math.floor(Math.random() * 16)]).join("")

      setResult(fakeHash)
    } catch (error) {
      setError("An error occurred while hashing the phone number")
    } finally {
      setIsLoading(false)
    }
  }

  const handleRetrievePhone = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!hashValue) {
      setError("Please enter a hash value")
      return
    }

    setError("")
    setIsLoading(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Generate a fake phone number for demo purposes
      const fakePhone = "+254" + Math.floor(Math.random() * 900000000 + 100000000)

      setResult(fakePhone)
    } catch (error) {
      setError("An error occurred while retrieving the phone number")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(result)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const clearForm = () => {
    setPhoneNumber("")
    setHashValue("")
    setResult("")
    setError("")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="border-b">
        <div className="container flex h-16 items-center justify-between px-4 md:px-6">
          <Link href="/" className="flex items-center gap-2 font-bold text-xl">
            <Shield className="h-6 w-6" />
            <span>MPesaHash</span>
          </Link>
          <nav className="flex items-center gap-4 sm:gap-6">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard">
                <Hash className="h-4 w-4 mr-2" />
                Dashboard
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/usage">
                <CreditCard className="h-4 w-4 mr-2" />
                Usage
              </Link>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/settings">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Link>
            </Button>
            <Button variant="ghost" size="icon">
              <User className="h-5 w-5" />
              <span className="sr-only">User menu</span>
            </Button>
            <Button variant="ghost" size="icon">
              <LogOut className="h-5 w-5" />
              <span className="sr-only">Log out</span>
            </Button>
          </nav>
        </div>
      </header>
      <main className="flex-1 container py-8">
        <div className="grid gap-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">Hash and retrieve MPesa phone numbers securely.</p>
          </div>

          <Tabs defaultValue="hash" value={activeTab} onValueChange={setActiveTab} className="w-full max-w-3xl mx-auto">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="hash">Hash Phone Number</TabsTrigger>
              <TabsTrigger value="retrieve">Retrieve Phone Number</TabsTrigger>
            </TabsList>
            <TabsContent value="hash">
              <Card>
                <CardHeader>
                  <CardTitle>Hash Phone Number</CardTitle>
                  <CardDescription>Convert an MPesa phone number into a secure hash value.</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleHashPhone} className="space-y-4">
                    {error && <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">{error}</div>}

                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber">Phone Number</Label>
                      <div className="flex gap-2">
                        <div className="relative flex-1">
                          <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="phoneNumber"
                            placeholder="+254 7XX XXX XXX"
                            className="pl-10"
                            value={phoneNumber}
                            onChange={(e) => setPhoneNumber(e.target.value)}
                          />
                        </div>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Hashing...
                            </>
                          ) : (
                            "Hash"
                          )}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Enter a valid Kenyan phone number (e.g., +254712345678 or 0712345678)
                      </p>
                    </div>

                    {result && (
                      <div className="space-y-2 mt-6">
                        <Label>Hash Result</Label>
                        <div className="relative">
                          <Input value={result} readOnly className="pr-10 font-mono text-xs" />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full"
                            onClick={copyToClipboard}
                          >
                            {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                            <span className="sr-only">Copy to clipboard</span>
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          This hash can be safely stored in your database.
                        </p>
                      </div>
                    )}
                  </form>
                </CardContent>
                {result && (
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" onClick={clearForm}>
                      Clear
                    </Button>
                    <p className="text-xs text-muted-foreground">Cost: 0.50 KSh</p>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
            <TabsContent value="retrieve">
              <Card>
                <CardHeader>
                  <CardTitle>Retrieve Phone Number</CardTitle>
                  <CardDescription>Convert a hash value back to the original MPesa phone number.</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleRetrievePhone} className="space-y-4">
                    {error && <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">{error}</div>}

                    <div className="space-y-2">
                      <Label htmlFor="hashValue">Hash Value</Label>
                      <div className="flex gap-2">
                        <div className="relative flex-1">
                          <Hash className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="hashValue"
                            placeholder="Enter hash value"
                            className="pl-10 font-mono text-xs"
                            value={hashValue}
                            onChange={(e) => setHashValue(e.target.value)}
                          />
                        </div>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? (
                            <>
                              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                              Retrieving...
                            </>
                          ) : (
                            "Retrieve"
                          )}
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Enter the hash value you want to convert back to a phone number
                      </p>
                    </div>

                    {result && (
                      <div className="space-y-2 mt-6">
                        <Label>Phone Number Result</Label>
                        <div className="relative">
                          <Input value={result} readOnly className="pr-10" />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full"
                            onClick={copyToClipboard}
                          >
                            {copied ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                            <span className="sr-only">Copy to clipboard</span>
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          This is the original phone number associated with the hash.
                        </p>
                      </div>
                    )}
                  </form>
                </CardContent>
                {result && (
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" onClick={clearForm}>
                      Clear
                    </Button>
                    <p className="text-xs text-muted-foreground">Cost: 0.50 KSh</p>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>
          </Tabs>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 max-w-3xl mx-auto">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                <Hash className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">127</div>
                <p className="text-xs text-muted-foreground">+19% from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">KSh 750.00</div>
                <p className="text-xs text-muted-foreground">Enough for 1,500 requests</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Status</CardTitle>
                <div className="flex h-2 w-2 rounded-full bg-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Operational</div>
                <p className="text-xs text-muted-foreground">100% uptime in the last 30 days</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <footer className="border-t py-6">
        <div className="container flex flex-col items-center justify-between gap-4 md:flex-row">
          <p className="text-sm text-muted-foreground">© {new Date().getFullYear()} MPesaHash. All rights reserved.</p>
          <p className="text-sm text-muted-foreground">
            Need help?{" "}
            <Link href="#" className="text-primary hover:underline">
              Contact support
            </Link>
          </p>
        </div>
      </footer>
    </div>
  )
}

